from flask import Blueprint, jsonify, request
from api.extensions import db
from api.models import Employee, Shift, ShiftAssignment, AttendanceLog
from datetime import datetime

# Create a blueprint for main routes
main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def home():
    """Home endpoint - API status"""
    return jsonify({
        'message': 'Smart Attendance Backend API',
        'status': 'running',
        'version': '1.0.0',
        'endpoints': {
            'employees': '/api/employees',
            'shifts': '/api/shifts',
            'attendance': '/api/attendance'
        }
    })

@main_bp.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'database': 'connected'
    })

# Employee routes
@main_bp.route('/api/employees', methods=['GET'])
def get_employees():
    """Get all employees"""
    employees = Employee.query.all()
    return jsonify([{
        'id': emp.id,
        'name': emp.name,
        'role': emp.role,
        'department': emp.department
    } for emp in employees])

@main_bp.route('/api/employees', methods=['POST'])
def create_employee():
    """Create a new employee"""
    data = request.get_json()
    
    if not data or not all(k in data for k in ('name', 'role', 'department')):
        return jsonify({'error': 'Missing required fields: name, role, department'}), 400
    
    employee = Employee(
        name=data['name'],
        role=data['role'],
        department=data['department']
    )
    
    db.session.add(employee)
    db.session.commit()
    
    return jsonify({
        'id': employee.id,
        'name': employee.name,
        'role': employee.role,
        'department': employee.department,
        'message': 'Employee created successfully'
    }), 201

@main_bp.route('/api/employees/<int:employee_id>', methods=['GET'])
def get_employee(employee_id):
    """Get a specific employee"""
    employee = Employee.query.get_or_404(employee_id)
    return jsonify({
        'id': employee.id,
        'name': employee.name,
        'role': employee.role,
        'department': employee.department
    })

# Shift routes
@main_bp.route('/api/shifts', methods=['GET'])
def get_shifts():
    """Get all shifts"""
    shifts = Shift.query.all()
    return jsonify([{
        'id': shift.id,
        'name': shift.name,
        'start_time': shift.start_time.strftime('%H:%M'),
        'end_time': shift.end_time.strftime('%H:%M')
    } for shift in shifts])

@main_bp.route('/api/shifts', methods=['POST'])
def create_shift():
    """Create a new shift"""
    data = request.get_json()
    
    if not data or not all(k in data for k in ('name', 'start_time', 'end_time')):
        return jsonify({'error': 'Missing required fields: name, start_time, end_time'}), 400
    
    try:
        start_time = datetime.strptime(data['start_time'], '%H:%M').time()
        end_time = datetime.strptime(data['end_time'], '%H:%M').time()
    except ValueError:
        return jsonify({'error': 'Invalid time format. Use HH:MM'}), 400
    
    shift = Shift(
        name=data['name'],
        start_time=start_time,
        end_time=end_time
    )
    
    db.session.add(shift)
    db.session.commit()
    
    return jsonify({
        'id': shift.id,
        'name': shift.name,
        'start_time': shift.start_time.strftime('%H:%M'),
        'end_time': shift.end_time.strftime('%H:%M'),
        'message': 'Shift created successfully'
    }), 201

# Attendance routes
@main_bp.route('/api/attendance', methods=['GET'])
def get_attendance():
    """Get all attendance logs"""
    logs = AttendanceLog.query.all()
    return jsonify([{
        'id': log.id,
        'employee_id': log.employee_id,
        'employee_name': log.employee.name,
        'timestamp': log.timestamp.isoformat(),
        'type': log.type
    } for log in logs])

@main_bp.route('/api/attendance', methods=['POST'])
def log_attendance():
    """Log attendance (entry/exit)"""
    data = request.get_json()
    
    if not data or not all(k in data for k in ('employee_id', 'type')):
        return jsonify({'error': 'Missing required fields: employee_id, type'}), 400
    
    if data['type'] not in ['entry', 'exit']:
        return jsonify({'error': 'Type must be either "entry" or "exit"'}), 400
    
    # Check if employee exists
    employee = Employee.query.get(data['employee_id'])
    if not employee:
        return jsonify({'error': 'Employee not found'}), 404
    
    attendance_log = AttendanceLog(
        employee_id=data['employee_id'],
        timestamp=datetime.now(),
        type=data['type']
    )
    
    db.session.add(attendance_log)
    db.session.commit()
    
    return jsonify({
        'id': attendance_log.id,
        'employee_id': attendance_log.employee_id,
        'employee_name': employee.name,
        'timestamp': attendance_log.timestamp.isoformat(),
        'type': attendance_log.type,
        'message': f'Attendance logged successfully - {data["type"]}'
    }), 201
