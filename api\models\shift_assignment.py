from api.extensions import db

class ShiftAssignment(db.Model):
    __tablename__ = 'shift_assignments'
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    employee_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('employees.id'), nullable=False)
    shift_id = db.<PERSON>umn(db.In<PERSON>ger, db.<PERSON><PERSON>('shifts.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)