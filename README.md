# Smart Attendance Backend

A Flask-based backend application for managing employee attendance with SQLite database.

## Features

- Employee management
- Shift management
- Shift assignments
- Attendance logging
- SQLite database with SQLAlchemy ORM

## Project Structure

```
smart-attendance-backend/
├── api/
│   ├── controllers/
│   ├── models/
│   │   ├── employee.py
│   │   ├── shift.py
│   │   ├── shift_assignment.py
│   │   └── attendance_log.py
│   ├── routes/
│   ├── extensions.py
│   └── main.py
├── venv_new/          # Virtual environment
├── main.py            # Application entry point
├── requirements.txt   # Python dependencies
├── setup.ps1         # Setup script
└── run.ps1           # Run script
```

## Setup Instructions

### Option 1: Using PowerShell Scripts (Recommended)

1. **Initial Setup:**
   ```powershell
   .\setup.ps1
   ```

2. **Run the Application:**
   ```powershell
   .\run.ps1
   ```

### Option 2: Manual Setup

1. **Create and activate virtual environment:**
   ```powershell
   python -m venv venv_new
   .\venv_new\Scripts\Activate.ps1
   ```

2. **Install dependencies:**
   ```powershell
   pip install -r requirements.txt
   ```

3. **Run the application:**
   ```powershell
   python main.py
   ```

## Running the Application

Once the application is running, you can access it at:
- Local: http://127.0.0.1:5000
- Network: http://************:5000 (or your local IP)

The application will automatically create the SQLite database (`attendance.db`) and all necessary tables on first run.

## Database Models

- **Employee**: Stores employee information (name, role, department)
- **Shift**: Defines work shifts (name, start_time, end_time)
- **ShiftAssignment**: Links employees to shifts for specific dates
- **AttendanceLog**: Records employee check-in/check-out times

## Development

The application runs in debug mode by default, which means:
- Automatic reloading when code changes
- Detailed error messages
- Debug toolbar available

## Requirements

- Python 3.12+
- Flask 3.1.1
- SQLAlchemy 2.0.42
- Other dependencies listed in requirements.txt
