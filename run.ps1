# Smart Attendance Backend Run Script
# This script activates the virtual environment and runs the application

Write-Host "Starting Smart Attendance Backend..." -ForegroundColor Green

# Check if virtual environment exists
if (-not (Test-Path "venv_new")) {
    Write-Host "Virtual environment not found. Please run setup.ps1 first." -ForegroundColor Red
    exit 1
}

# Activate virtual environment and run the application
& .\venv_new\Scripts\Activate.ps1
python main.py
