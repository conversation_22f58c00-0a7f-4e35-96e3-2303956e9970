from flask import Flask
from api.extensions import db
from api.models import Employee, Shift, ShiftAssignment, AttendanceLog  # Import models so they register with SQLAlchemy
from api.routes.main_routes import main_bp

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///attendance.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db.init_app(app)  # Correct way to initialize db with app

# Register blueprints
app.register_blueprint(main_bp)

with app.app_context():
    db.create_all()

if __name__ == '__main__':
    app.run(debug=True)
