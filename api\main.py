from flask import Flask
from api.extensions import db
from api.models import Employee  # Import models so they register with SQLAlchemy

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///attendance.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db.init_app(app)  # Correct way to initialize db with app

with app.app_context():
    db.create_all()

if __name__ == '__main__':
    app.run(debug=True)
