# Smart Attendance Backend Setup Script
# This script sets up the virtual environment and installs dependencies

Write-Host "Setting up Smart Attendance Backend..." -ForegroundColor Green

# Check if Python is installed
try {
    $pythonVersion = python --version
    Write-Host "Found Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "Python is not installed or not in PATH. Please install Python first." -ForegroundColor Red
    exit 1
}

# Create virtual environment if it doesn't exist
if (-not (Test-Path "venv_new")) {
    Write-Host "Creating virtual environment..." -ForegroundColor Yellow
    python -m venv venv_new
} else {
    Write-Host "Virtual environment already exists." -ForegroundColor Green
}

# Activate virtual environment and install dependencies
Write-Host "Activating virtual environment and installing dependencies..." -ForegroundColor Yellow
& .\venv_new\Scripts\Activate.ps1
pip install -r requirements.txt

Write-Host "Setup complete!" -ForegroundColor Green
Write-Host "To run the application:" -ForegroundColor Cyan
Write-Host "1. Activate the virtual environment: .\venv_new\Scripts\Activate.ps1" -ForegroundColor Cyan
Write-Host "2. Run the application: python main.py" -ForegroundColor Cyan
Write-Host "Or simply run: .\run.ps1" -ForegroundColor Cyan
