from . import db

class Employee(db.Model):
    __tablename__ = 'employees'
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(50), nullable=False)
    department = db.Column(db.String(50), nullable=False)

    shift_assignments = db.relationship('ShiftAssignment', backref='employee', lazy=True)
    attendance_logs = db.relationship('AttendanceLog', backref='employee', lazy=True)
